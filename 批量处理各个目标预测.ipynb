{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-19T08:29:48.633488500Z", "start_time": "2025-06-19T08:29:37.916932Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n", "成功加载年度ICA特征数据，形状: (64, 64)\n", "选择的疾病：Acute glomerulonephritis\n", "原始目标特征数量: 386\n", "总输入特征数量 (原始 + ICA): 450\n", "训练数据年份: 1990-2011, 形状: (22, 450)\n", "测试评估数据年份: 2012-2021, 形状: (10, 450)\n", "LNN 模型参数数量: 91138\n", "\n", "开始 Seq2Seq LNN-Attention 模型训练 (学习偏差)...\n", "Epoch [10/200], Loss: 0.136935\n", "Epoch [20/200], Loss: 0.093552\n", "Epoch [30/200], Loss: 0.067421\n", "Epoch [40/200], Loss: 0.050338\n", "Epoch [50/200], Loss: 0.036998\n", "Epoch [60/200], Loss: 0.029707\n", "Epoch [70/200], Loss: 0.024840\n", "Epoch [80/200], Loss: 0.019582\n", "Epoch [90/200], Loss: 0.018423\n", "Epoch [100/200], Loss: 0.016277\n", "Epoch [110/200], Loss: 0.014580\n", "Epoch [120/200], Loss: 0.013143\n", "Epoch [130/200], Loss: 0.011228\n", "Epoch [140/200], Loss: 0.009955\n", "Epoch [150/200], Loss: 0.009500\n", "Epoch [160/200], Loss: 0.008486\n", "Epoch [170/200], Loss: 0.007888\n", "Epoch [180/200], Loss: 0.007351\n", "Epoch [190/200], Loss: 0.006779\n", "Epoch [200/200], Loss: 0.006375\n", "\n", "开始迭代多步预测 (LNN 偏差模型)...\n", "  年份 2013: R2: 0.980, MSE: 0.000, MAE: 0.000, MAPE: 15.27%\n", "  年份 2015: R2: 0.896, MSE: 0.000, MAE: 0.000, MAPE: 25.36%\n", "  年份 2017: R2: 0.726, MSE: 0.000, MAE: 0.000, MAPE: 32.30%\n", "  年份 2019: R2: 0.558, MSE: 0.000, MAE: 0.000, MAPE: 40.54%\n", "  年份 2021: R2: 0.503, MSE: 0.000, MAE: 0.000, MAPE: 49.43%\n", "\n", "--- 年度评估指标 (Seq2Seq LNN-Attention 偏差模型 + ICA特征) ---\n", "      R2_Score           MSE       MAE       MAPE\n", "Year                                             \n", "2012  0.994320  2.428905e-11  0.000002  11.046445\n", "2013  0.979677  8.228549e-11  0.000004  15.272442\n", "2014  0.948195  1.998724e-10  0.000006  20.286204\n", "2015  0.895937  3.913585e-10  0.000009  25.355888\n", "2016  0.819236  6.896420e-10  0.000011  28.474166\n", "2017  0.726213  1.071910e-09  0.000013  32.300127\n", "2018  0.630111  1.488925e-09  0.000015  36.790744\n", "2019  0.558112  1.858898e-09  0.000017  40.535405\n", "2020  0.533631  1.878932e-09  0.000018  45.149110\n", "2021  0.502773  1.876059e-09  0.000019  49.432716\n", "\n", "平均 R2: 0.7588\n", "平均 MSE: 0.0000\n", "平均 MAE: 0.0000\n", "平均 MAPE: 30.46%\n", "\n", "--- 所有年份的迭代预测值 (原始尺度, 前5个目标特征) ---\n", "     Afghanistan-Female Afghanistan-Male Albania-Female Albania-Male  \\\n", "2012           0.000157         0.000083       0.000063     0.000089   \n", "2013           0.000157         0.000078       0.000056     0.000083   \n", "2014           0.000156         0.000073       0.000053     0.000079   \n", "2015           0.000154         0.000068       0.000053     0.000078   \n", "2016           0.000153         0.000064       0.000054      0.00008   \n", "2017           0.000153         0.000059       0.000051      0.00008   \n", "2018           0.000153         0.000054       0.000047     0.000077   \n", "2019           0.000152         0.000049       0.000045     0.000075   \n", "2020           0.000151         0.000044       0.000044     0.000075   \n", "2021            0.00015         0.000039       0.000043     0.000074   \n", "\n", "     Algeria-Female  \n", "2012       0.000015  \n", "2013       0.000015  \n", "2014       0.000015  \n", "2015       0.000015  \n", "2016       0.000014  \n", "2017       0.000014  \n", "2018       0.000014  \n", "2019       0.000014  \n", "2020       0.000014  \n", "2021       0.000014  \n"]}, {"data": {"text/plain": "<Figure size 1000x600 with 1 Axes>", "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": "<Figure size 1500x700 with 1 Axes>", "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import MinMaxScaler\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, Dataset\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# --- 1. 参数设置 (基本不变) ---\n", "selected_age = \"60_to_64\"\n", "SEQUENCE_LENGTH = 5\n", "ENCODER_HIDDEN_SIZE = 64 # 可以保持不变，或者根据特征数量增加\n", "NUM_ENCODER_LAYERS = 1\n", "LEARNING_RATE = 0.0001\n", "EPOCHS = 200\n", "BATCH_SIZE = 4\n", "SOLVER_ITERATIONS = 3\n", "\n", "TRAIN_START_YEAR = 1990\n", "TRAIN_END_YEAR = 2011\n", "TEST_START_YEAR = 2012\n", "TEST_END_YEAR = 2021\n", "N_FORECAST_YEARS = TEST_END_YEAR - TEST_START_YEAR + 1\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")\n", "torch.manual_seed(42)\n", "np.random.seed(42)\n", "\n", "# --- 2. 数据加载和预处理 ---\n", "\n", "# 【新增】加载预先计算好的年度ICA特征\n", "try:\n", "    ica_features_df = pd.read_csv('ICA处理合并特征值.csv', index_col=0) \n", "    if ica_features_df.index.name != 'year':\n", "        ica_features_df.index.name = 'year'\n", "    print(f\"成功加载年度ICA特征数据，形状: {ica_features_df.shape}\")\n", "except FileNotFoundError:\n", "    print(\"错误: 找不到 'ICA处理合并特征值.csv' 文件。请先运行特征提取代码并保存结果。\")\n", "    exit()\n", "\n", "all_select=[\"DALYs (Disability-Adjusted Life Years)\",\"Deaths\",\"Incidence\",\"Prevalence\"]\n", "target=all_select[0]\n", "disease_folders = sorted(os.listdir(f\"数据分割_{target}\"))[1:2]\n", "disease = disease_folders[0]\n", "print(f\"选择的疾病：{disease}\")\n", "age_file = f\"{selected_age}.csv\"\n", "file_path = os.path.join(f\"数据分割_{target}\", disease, age_file)\n", "df = pd.read_csv(file_path, index_col=\"year\")\n", "\n", "# 【修改】将原始数据与年度ICA特征合并\n", "df_merged = df.join(ica_features_df, how='left')\n", "\n", "# 【修改】处理合并后可能产生的NaN（如果年份不完全对齐）\n", "# 这里我们继续使用0填充，保持与原代码逻辑一致\n", "df_processed = df_merged.fillna(0)\n", "\n", "# 【修改】区分原始目标特征和所有输入特征\n", "original_numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "all_numeric_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()\n", "N_TARGET_FEATURES = len(original_numeric_cols)\n", "N_TOTAL_FEATURES = len(all_numeric_cols)\n", "\n", "print(f\"原始目标特征数量: {N_TARGET_FEATURES}\")\n", "print(f\"总输入特征数量 (原始 + ICA): {N_TOTAL_FEATURES}\")\n", "\n", "# 分割训练和测试集\n", "train_data_pd = df_processed.loc[TRAIN_START_YEAR:TRAIN_END_YEAR]\n", "test_data_pd_for_eval = df_processed.loc[TEST_START_YEAR:TEST_END_YEAR]\n", "print(f\"训练数据年份: {train_data_pd.index.min()}-{train_data_pd.index.max()}, 形状: {train_data_pd.shape}\")\n", "print(f\"测试评估数据年份: {test_data_pd_for_eval.index.min()}-{test_data_pd_for_eval.index.max()}, 形状: {test_data_pd_for_eval.shape}\")\n", "\n", "# 数据缩放 (对所有特征进行)\n", "scalers = {}\n", "scaled_train_data = train_data_pd.copy()\n", "for col in all_numeric_cols:\n", "    scaler = MinMaxScaler(feature_range=(-1, 1))\n", "    scaled_train_data[col] = scaler.fit_transform(train_data_pd[[col]].values)\n", "    scalers[col] = scaler\n", "\n", "# 【修改】将整个处理后的数据也进行缩放，用于预测阶段提取真实的ICA特征\n", "scaled_full_data = df_processed.copy()\n", "for col in all_numeric_cols:\n", "    # 使用在训练集上fit的scaler来transform整个数据集\n", "    # 避免数据泄露\n", "    if col in scalers:\n", "        scaled_full_data[col] = scalers[col].transform(df_processed[[col]].values)\n", "    else: # 以防万一有列不在训练集中\n", "        scaled_full_data[col] = 0\n", "\n", "# --- 3. Dataset 定义 ---\n", "# 【修改】Dataset需要知道目标特征的数量，以便正确切分 target\n", "class TimeSeriesDeviationDataset(Dataset):\n", "    def __init__(self, data_array, sequence_length, n_target_features):\n", "        self.data_array = data_array\n", "        self.sequence_length = sequence_length\n", "        self.n_target_features = n_target_features # 新增\n", "        if len(self.data_array) <= self.sequence_length:\n", "            raise ValueError(f\"数据长度 ({len(self.data_array)}) 必须大于序列长度 ({self.sequence_length}).\")\n", "    def __len__(self):\n", "        return len(self.data_array) - self.sequence_length\n", "    def __getitem__(self, idx):\n", "        # 输入X包含所有特征 (原始 + ICA)\n", "        x_window_scaled = self.data_array[idx : idx + self.sequence_length]\n", "        \n", "        # 目标y只包含原始特征\n", "        y_target_full = self.data_array[idx + self.sequence_length]\n", "        y_target_scaled = y_target_full[:self.n_target_features] # 切片！只取目标特征\n", "\n", "        # 计算偏差\n", "        mean_of_x_window_scaled = np.mean(x_window_scaled, axis=0)\n", "        x_deviation_scaled = x_window_scaled - mean_of_x_window_scaled\n", "        \n", "        # 目标偏差只相对于目标的均值计算\n", "        mean_of_y_window_scaled = mean_of_x_window_scaled[:self.n_target_features]\n", "        y_deviation_scaled = y_target_scaled - mean_of_y_window_scaled\n", "        \n", "        return (torch.FloatTensor(x_deviation_scaled).to(device),\n", "                torch.FloatTensor(mean_of_x_window_scaled).to(device),\n", "                torch.FloatTensor(y_deviation_scaled).to(device))\n", "\n", "train_numpy_scaled = scaled_train_data.values\n", "# 【修改】在创建Dataset时传入目标特征数\n", "train_dataset = TimeSeriesDeviationDataset(train_numpy_scaled, SEQUENCE_LENGTH, N_TARGET_FEATURES)\n", "if len(train_dataset) == 0:\n", "    print(f\"警告: 训练数据集为空。\")\n", "    exit()\n", "train_dataloader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)\n", "\n", "\n", "# --- 4. 模型定义 (基本不变，但实例化时参数会变) ---\n", "class LTCishCell(nn.Module):\n", "    def __init__(self, input_size, hidden_size, solver_iterations=3, activation_fn=nn.Tanh):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.input_size = input_size\n", "        self.hidden_size = hidden_size\n", "        self.solver_iterations = solver_iterations\n", "        self.dt = 1.0 / solver_iterations\n", "        self.input_to_hidden = nn.Linear(input_size, hidden_size, bias=True)\n", "        self.hidden_to_hidden = nn.Linear(hidden_size, hidden_size, bias=False)\n", "        self.log_tau = nn.Parameter(torch.randn(hidden_size) * 0.1)\n", "        self.activation = activation_fn()\n", "    def forward(self, x_t, h_prev):\n", "        tau = torch.exp(self.log_tau)\n", "        tau = torch.clamp(tau, min=self.dt * 1.1)\n", "        pre_activation = self.input_to_hidden(x_t) + self.hidden_to_hidden(h_prev)\n", "        target_h = self.activation(pre_activation)\n", "        h_next = h_prev\n", "        for _ in range(self.solver_iterations):\n", "            h_dot = (1.0 / tau) * (target_h - h_next)\n", "            h_next = h_next + self.dt * h_dot\n", "        return h_next\n", "\n", "class LNNEncoder(nn.Module):\n", "    def __init__(self, input_size, hidden_size, num_layers, solver_iterations=3, activation_fn=nn.Tanh):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "        self.layers = nn.ModuleList()\n", "        current_input_size = input_size\n", "        for _ in range(num_layers):\n", "            self.layers.append(LTCishCell(current_input_size, hidden_size, solver_iterations, activation_fn))\n", "            current_input_size = hidden_size\n", "    def forward(self, x):\n", "        batch_size, seq_len, _ = x.shape\n", "        final_layer_hidden_states = []\n", "        current_sequence_input = x\n", "        for layer_idx in range(self.num_layers):\n", "            h_t = torch.zeros(batch_size, self.hidden_size, device=x.device)\n", "            layer_output_sequence = []\n", "            for t in range(seq_len):\n", "                x_t_for_layer = current_sequence_input[:, t, :]\n", "                h_t = self.layers[layer_idx](x_t_for_layer, h_t)\n", "                layer_output_sequence.append(h_t.unsqueeze(1))\n", "            current_sequence_input = torch.cat(layer_output_sequence, dim=1)\n", "            final_layer_hidden_states.append(h_t)\n", "        encoder_outputs = current_sequence_input\n", "        final_hidden_state = torch.stack(final_layer_hidden_states, dim=0)\n", "        final_cell_state = torch.zeros_like(final_hidden_state)\n", "        return encoder_outputs, (final_hidden_state, final_cell_state)\n", "\n", "class Attention(nn.Module):\n", "    def __init__(self, hidden_size):\n", "        super(Attention, self).__init__()\n", "        self.attn = nn.Linear(hidden_size * 2, hidden_size)\n", "        self.v = nn.Parameter(torch.rand(hidden_size))\n", "        torch.nn.init.xavier_uniform_(self.attn.weight)\n", "        torch.nn.init.zeros_(self.attn.bias)\n", "    def forward(self, query_hidden, encoder_outputs):\n", "        seq_len = encoder_outputs.size(1)\n", "        query_hidden_repeated = query_hidden.unsqueeze(1).repeat(1, seq_len, 1)\n", "        energy_input = torch.cat((query_hidden_repeated, encoder_outputs), dim=2)\n", "        energy = torch.tanh(self.attn(energy_input))\n", "        attention_scores = torch.sum(self.v * energy, dim=2)\n", "        attention_weights = torch.softmax(attention_scores, dim=1).unsqueeze(1)\n", "        context = torch.bmm(attention_weights, encoder_outputs)\n", "        context = context.squeeze(1)\n", "        return context, attention_weights.squeeze(1)\n", "\n", "class Seq2SeqLNN_AttentionForecastModel(nn.Module):\n", "    def __init__(self, input_size, encoder_hidden_size, output_size, num_encoder_layers=1, solver_iterations=3, activation_fn_choice='tanh'):\n", "        super(Seq2SeqLNN_AttentionForecastModel, self).__init__()\n", "        if activation_fn_choice.lower() == 'tanh': activation_function = nn.Tanh\n", "        elif activation_fn_choice.lower() == 'relu': activation_function = nn.ReLU\n", "        elif activation_fn_choice.lower() == 'sigmoid': activation_function = nn.Sigmoid\n", "        else: activation_function = nn.Tanh\n", "        self.encoder = LNNEncoder(input_size, encoder_hidden_size, num_encoder_layers, solver_iterations, activation_fn=activation_function)\n", "        self.attention = Attention(encoder_hidden_size)\n", "        self.fc = nn.Linear(encoder_hidden_size * 2, output_size)\n", "        torch.nn.init.xavier_uniform_(self.fc.weight)\n", "        torch.nn.init.zeros_(self.fc.bias)\n", "    def forward(self, x_deviation):\n", "        encoder_outputs, (encoder_hidden_state, _) = self.encoder(x_deviation)\n", "        query_hidden = encoder_hidden_state[-1, :, :]\n", "        context_vector, _ = self.attention(query_hidden, encoder_outputs)\n", "        combined_representation = torch.cat((query_hidden, context_vector), dim=1)\n", "        predicted_deviation = self.fc(combined_representation)\n", "        return predicted_deviation\n", "\n", "# 【修改】实例化模型时，传入正确的 input_size 和 output_size\n", "model = Seq2SeqLNN_AttentionForecastModel(\n", "    input_size=N_TOTAL_FEATURES,      # 输入是全部特征\n", "    encoder_hidden_size=ENCODER_HIDDEN_SIZE,\n", "    output_size=N_TARGET_FEATURES,    # 输出只是原始目标特征\n", "    num_encoder_layers=NUM_ENCODER_LAYERS,\n", "    solver_iterations=SOLVER_ITERATIONS,\n", "    activation_fn_choice='tanh'\n", ").to(device)\n", "\n", "total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"LNN 模型参数数量: {total_params}\")\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)\n", "\n", "# --- 5. 模型训练 (代码不变) ---\n", "print(\"\\n开始 Seq2Seq LNN-Attention 模型训练 (学习偏差)...\")\n", "# (训练部分代码无需修改，因为Dataset已经处理好了输入和输出的维度)\n", "training_losses = []\n", "for epoch in range(EPOCHS):\n", "    model.train()\n", "    epoch_loss = 0\n", "    for i, (x_deviations, means, y_deviations_targets) in enumerate(train_dataloader):\n", "        optimizer.zero_grad()\n", "        predicted_deviations = model(x_deviations)\n", "        loss = criterion(predicted_deviations, y_deviations_targets)\n", "        loss.backward()\n", "        optimizer.step()\n", "        epoch_loss += loss.item()\n", "    avg_epoch_loss = epoch_loss / len(train_dataloader)\n", "    training_losses.append(avg_epoch_loss)\n", "    if (epoch + 1) % 10 == 0:\n", "        print(f\"Epoch [{epoch+1}/{EPOCHS}], Loss: {avg_epoch_loss:.6f}\")\n", "\n", "# --- 6. 多步迭代预测 ---\n", "# 【重大修改】预测循环的逻辑需要更新\n", "def mean_absolute_percentage_error(y_true, y_pred):\n", "    y_true, y_pred = np.array(y_true), np.array(y_pred)\n", "    mask = y_true != 0\n", "    if not np.any(mask): return 0.0\n", "    return np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100\n", "\n", "print(\"\\n开始迭代多步预测 (LNN 偏差模型)...\")\n", "model.eval()\n", "yearly_metrics_list = []\n", "# 【修改】预测结果的DataFrame只包含原始目标列\n", "all_predictions_original_scale_df = pd.DataFrame(columns=original_numeric_cols,\n", "                                                 index=range(TEST_START_YEAR, TEST_END_YEAR + 1))\n", "current_input_sequence_scaled_numpy = scaled_train_data.values[-SEQUENCE_LENGTH:]\n", "\n", "with torch.no_grad():\n", "    for i in range(N_FORECAST_YEARS):\n", "        current_predict_year = TEST_START_YEAR + i\n", "\n", "        # 1. 准备输入\n", "        mean_of_current_input_scaled = np.mean(current_input_sequence_scaled_numpy, axis=0)\n", "        input_deviation_scaled_numpy = current_input_sequence_scaled_numpy - mean_of_current_input_scaled\n", "        input_deviation_tensor = torch.FloatTensor(input_deviation_scaled_numpy).unsqueeze(0).to(device)\n", "        \n", "        # 2. 模型预测偏差 (输出维度为 N_TARGET_FEATURES)\n", "        predicted_deviation_scaled_tensor = model(input_deviation_tensor)\n", "        predicted_deviation_scaled_numpy_flat = predicted_deviation_scaled_tensor.cpu().numpy().flatten()\n", "        \n", "        # 3. 重构预测值 (只重构目标特征部分)\n", "        mean_for_target = mean_of_current_input_scaled[:N_TARGET_FEATURES]\n", "        predicted_target_features_scaled = predicted_deviation_scaled_numpy_flat + mean_for_target\n", "\n", "        # 4. 反归一化，得到原始尺度的预测值 (只对目标特征)\n", "        forecast_original_scale_year = []\n", "        for k, col_name in enumerate(original_numeric_cols):\n", "            val_scaled = predicted_target_features_scaled[k].reshape(-1, 1)\n", "            val_original = scalers[col_name].inverse_transform(val_scaled)\n", "            forecast_original_scale_year.append(val_original[0, 0])\n", "        \n", "        all_predictions_original_scale_df.loc[current_predict_year] = forecast_original_scale_year\n", "        \n", "        # 5. 【关键】准备下一次迭代的输入序列\n", "        #    - 使用刚刚预测出的目标特征\n", "        #    - 使用已知的、真实的ICA特征\n", "        if current_predict_year in scaled_full_data.index:\n", "            # 从缩放后的完整数据集中获取真实的ICA特征\n", "            true_ica_features_scaled = scaled_full_data.loc[current_predict_year, all_numeric_cols[N_TARGET_FEATURES:]].values\n", "        else:\n", "            # 如果某年份数据缺失，用0填充\n", "            true_ica_features_scaled = np.zeros(N_TOTAL_FEATURES - N_TARGET_FEATURES)\n", "\n", "        # 拼接成下一年的完整特征向量 (预测的目标 + 真实的ICA)\n", "        next_full_year_scaled = np.concatenate([predicted_target_features_scaled, true_ica_features_scaled])\n", "        \n", "        # 更新输入序列\n", "        current_input_sequence_scaled_numpy = np.vstack([\n", "            current_input_sequence_scaled_numpy[1:],\n", "            next_full_year_scaled.reshape(1, -1)\n", "        ])\n", "\n", "        # 6. 评估 (逻辑不变，因为只评估目标特征)\n", "        if current_predict_year in test_data_pd_for_eval.index:\n", "            current_year_true_values_array = test_data_pd_for_eval.loc[current_predict_year, original_numeric_cols].values.flatten()\n", "            current_year_forecast_array_for_eval = np.array(forecast_original_scale_year).flatten()\n", "            metrics = {'Year': current_predict_year, 'R2_Score': r2_score(current_year_true_values_array, current_year_forecast_array_for_eval),\n", "                       'MSE': mean_squared_error(current_year_true_values_array, current_year_forecast_array_for_eval),\n", "                       'MAE': mean_absolute_error(current_year_true_values_array, current_year_forecast_array_for_eval),\n", "                       'MAPE': mean_absolute_percentage_error(current_year_true_values_array, current_year_forecast_array_for_eval)}\n", "            yearly_metrics_list.append(metrics)\n", "            if (i+1) % 2 == 0 or i == N_FORECAST_YEARS - 1:\n", "                print(f\"  年份 {current_predict_year}: R2: {metrics['R2_Score']:.3f}, MSE: {metrics['MSE']:.3f}, MAE: {metrics['MAE']:.3f}, MAPE: {metrics['MAPE']:.2f}%\")\n", "\n", "\n", "# --- 7. 结果展示 (基本不变) ---\n", "metrics_df = pd.DataFrame(yearly_metrics_list).set_index('Year')\n", "print(\"\\n--- 年度评估指标 (Seq2Seq LNN-Attention 偏差模型 + ICA特征) ---\")\n", "print(metrics_df)\n", "print(f\"\\n平均 R2: {metrics_df['R2_Score'].mean():.4f}\")\n", "print(f\"平均 MSE: {metrics_df['MSE'].mean():.4f}\")\n", "print(f\"平均 MAE: {metrics_df['MAE'].mean():.4f}\")\n", "print(f\"平均 MAPE: {metrics_df['MAPE'].mean():.2f}%\")\n", "\n", "# (绘图部分代码无需修改)\n", "if not metrics_df['R2_Score'].isnull().all():\n", "    plt.figure(figsize=(10, 6))\n", "    metrics_df['R2_Score'].plot(kind='bar', color='deepskyblue', edgecolor='black')\n", "    plt.title(f'{disease} - {selected_age} - Seq2Seq LNN-Attention (含ICA) 迭代预测逐年R²')\n", "    plt.xlabel(\"年份\")\n", "    plt.ylabel(\"R² 分数\")\n", "    plt.axhline(0, color='grey', lw=1, linestyle='--')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(axis='y', linestyle=':', alpha=0.7)\n", "    plt.tight_layout()\n", "\n", "# 【修改】绘图时，从 train_data_pd 和 test_data_pd_for_eval 中选择原始特征列进行绘图\n", "if not all_predictions_original_scale_df.empty and N_TARGET_FEATURES > 0:\n", "    feature_to_plot_idx = 0 \n", "    feature_name_to_plot = original_numeric_cols[feature_to_plot_idx]\n", "    plt.figure(figsize=(15, 7))\n", "    plt.plot(train_data_pd.index, train_data_pd[feature_name_to_plot], label=f\"训练数据\", marker='.', linestyle='-', color='gray')\n", "    if not test_data_pd_for_eval.empty:\n", "        plt.plot(test_data_pd_for_eval.index, test_data_pd_for_eval[feature_name_to_plot], label=f\"真实值 (测试期)\", marker='o', linestyle='-', color='green')\n", "    plt.plot(all_predictions_original_scale_df.index, all_predictions_original_scale_df[feature_name_to_plot], label=f\"LNN (含ICA) 预测值\", marker='x', linestyle='--', color='dodgerblue')\n", "    plt.title(f\"{disease} - {selected_age} - {feature_name_to_plot} LNN (含ICA) 迭代预测对比\")\n", "    plt.xlabel(\"年份\")\n", "    plt.ylabel(\"值\")\n", "    plt.legend()\n", "    plt.grid(True, linestyle=':', alpha=0.7)\n", "    plt.tight_layout()\n", "\n", "print(\"\\n--- 所有年份的迭代预测值 (原始尺度, 前5个目标特征) ---\")\n", "print(all_predictions_original_scale_df.iloc[:, :min(5, N_TARGET_FEATURES)].head(N_FORECAST_YEARS))\n", "\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cuda\n", "成功加载年度ICA特征数据，形状: (64, 64)\n", "\n", "\n", "######################### Processing Disease: Acne vulgaris #########################\n", "预测结果将保存在: ICA_Incidence_prediction_Acne_vulgaris\n", "\n", "\n", "==================== Processing Age Group: under_5 (1/20) ====================\n", "  !! 文件未找到: 数据分割_Incidence\\Acne vulgaris\\under_5.csv。跳过此年龄段。\n", "\n", "\n", "==================== Processing Age Group: 5_to_9 (2/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 5_to_9 进行模型训练...\n", "\n", "  开始为 5_to_9 进行迭代多步预测...\n", "--- 年龄段 5_to_9 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 10_to_14 (3/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 10_to_14 进行模型训练...\n", "\n", "  开始为 10_to_14 进行迭代多步预测...\n", "--- 年龄段 10_to_14 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 15_to_19 (4/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 15_to_19 进行模型训练...\n", "\n", "  开始为 15_to_19 进行迭代多步预测...\n", "--- 年龄段 15_to_19 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 20_to_24 (5/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 20_to_24 进行模型训练...\n", "\n", "  开始为 20_to_24 进行迭代多步预测...\n", "--- 年龄段 20_to_24 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 25_to_29 (6/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 25_to_29 进行模型训练...\n", "\n", "  开始为 25_to_29 进行迭代多步预测...\n", "--- 年龄段 25_to_29 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 30_to_34 (7/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 30_to_34 进行模型训练...\n", "\n", "  开始为 30_to_34 进行迭代多步预测...\n", "--- 年龄段 30_to_34 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 35_to_39 (8/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 35_to_39 进行模型训练...\n", "\n", "  开始为 35_to_39 进行迭代多步预测...\n", "--- 年龄段 35_to_39 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 40_to_44 (9/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 40_to_44 进行模型训练...\n", "\n", "  开始为 40_to_44 进行迭代多步预测...\n", "--- 年龄段 40_to_44 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 45_to_49 (10/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 45_to_49 进行模型训练...\n", "\n", "  开始为 45_to_49 进行迭代多步预测...\n", "--- 年龄段 45_to_49 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 50_to_54 (11/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 50_to_54 进行模型训练...\n", "\n", "  开始为 50_to_54 进行迭代多步预测...\n", "--- 年龄段 50_to_54 处理完毕 ---\n", "\n", "\n", "==================== Processing Age Group: 55_to_59 (12/20) ====================\n", "  原始目标特征数量: 386, 总输入特征数量: 450\n", "\n", "  开始为 55_to_59 进行模型训练...\n"]}], "source": ["import os\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.preprocessing import MinMaxScaler\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "import shutil\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import DataLoader, Dataset\n", "\n", "# --- 1. 全局设置与类定义 ---\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# --- 全局参数 ---\n", "SEQUENCE_LENGTH = 5\n", "ENCODER_HIDDEN_SIZE = 64\n", "NUM_ENCODER_LAYERS = 1\n", "LEARNING_RATE = 0.0001\n", "EPOCHS = 150\n", "BATCH_SIZE = 4\n", "SOLVER_ITERATIONS = 3\n", "ACTIVATION_FN_CHOICE = 'tanh'\n", "\n", "TRAIN_START_YEAR = 1990\n", "TRAIN_END_YEAR = 2011\n", "TEST_START_YEAR = 2012\n", "TEST_END_YEAR = 2021\n", "N_FORECAST_YEARS = TEST_END_YEAR - TEST_START_YEAR + 1\n", "\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"Using device: {device}\")\n", "\n", "standard_age_groups = [\n", "    'under_5', '5_to_9', '10_to_14', '15_to_19', '20_to_24',\n", "    '25_to_29', '30_to_34', '35_to_39', '40_to_44', '45_to_49',\n", "    '50_to_54', '55_to_59', '60_to_64', '65_to_69', '70_to_74',\n", "    '75_to_79', '80_to_84', '85_to_89', '90_to_94', '95_plus'\n", "]\n", "\n", "# --- 【新增】在所有循环外加载ICA特征 ---\n", "try:\n", "    ica_features_df = pd.read_csv('ICA处理合并特征值.csv', index_col=0)\n", "    if ica_features_df.index.name != 'year':\n", "        ica_features_df.index.name = 'year'\n", "    print(f\"成功加载年度ICA特征数据，形状: {ica_features_df.shape}\")\n", "except FileNotFoundError:\n", "    print(\"错误: 找不到 'result_df.csv' 文件。请先运行特征提取代码并保存结果。\")\n", "    exit()\n", "\n", "# --- 【修改】将类定义移到循环外 ---\n", "\n", "class TimeSeriesDeviationDataset(Dataset):\n", "    \"\"\"修改后的Dataset，处理总输入特征和目标特征的分离\"\"\"\n", "    def __init__(self, data_array, sequence_length, n_target_features):\n", "        self.data_array = data_array\n", "        self.sequence_length = sequence_length\n", "        self.n_target_features = n_target_features\n", "        if len(self.data_array) <= self.sequence_length:\n", "            raise ValueError(f\"数据长度 ({len(self.data_array)}) 必须大于序列长度 ({self.sequence_length}).\")\n", "    def __len__(self):\n", "        return len(self.data_array) - self.sequence_length\n", "    def __getitem__(self, idx):\n", "        x_window_scaled = self.data_array[idx : idx + self.sequence_length]\n", "        y_target_full = self.data_array[idx + self.sequence_length]\n", "        y_target_scaled = y_target_full[:self.n_target_features]\n", "        mean_of_x_window_scaled = np.mean(x_window_scaled, axis=0)\n", "        x_deviation_scaled = x_window_scaled - mean_of_x_window_scaled\n", "        mean_of_y_window_scaled = mean_of_x_window_scaled[:self.n_target_features]\n", "        y_deviation_scaled = y_target_scaled - mean_of_y_window_scaled\n", "        return (torch.FloatTensor(x_deviation_scaled).to(device),\n", "                torch.FloatTensor(mean_of_x_window_scaled).to(device),\n", "                torch.FloatTensor(y_deviation_scaled).to(device))\n", "\n", "\n", "# --- 2. 主循环 ---\n", "\n", "all_select=[\"DALYs (Disability-Adjusted Life Years)\",\"Deaths\",\"Incidence\",\"Prevalence\"]\n", "target=all_select[2] # 例如选择 'Deaths'\n", "disease_folders_all = sorted(os.listdir(f\"数据分割_{target}\"))\n", "\n", "for target_disease_name in disease_folders_all[:]: # 可以切片选择部分疾病进行测试, e.g., [:3]\n", "    print(f\"\\n\\n{'#'*25} Processing Disease: {target_disease_name} {'#'*25}\")\n", "    \n", "    output_base_folder = f\"ICA_{target}_prediction_{target_disease_name.replace(' ', '_').replace(',', '')}\"\n", "    if os.path.exists(output_base_folder):\n", "        shutil.rmtree(output_base_folder)\n", "    os.makedirs(output_base_folder, exist_ok=True)\n", "    print(f\"预测结果将保存在: {output_base_folder}\")\n", "\n", "    all_age_group_metrics = []\n", "    \n", "    for age_idx, selected_age in enumerate(standard_age_groups):\n", "        print(f\"\\n\\n{'='*20} Processing Age Group: {selected_age} ({age_idx+1}/{len(standard_age_groups)}) {'='*20}\")\n", "    \n", "        torch.manual_seed(42 + age_idx); np.random.seed(42 + age_idx)\n", "    \n", "        # --- 2.1 数据加载与合并 ---\n", "        file_path = os.path.join(f\"数据分割_{target}\", target_disease_name, f\"{selected_age}.csv\")\n", "        if not os.path.exists(file_path):\n", "            print(f\"  !! 文件未找到: {file_path}。跳过此年龄段。\")\n", "            all_age_group_metrics.append({'Age_Group': selected_age, 'Status': 'File Not Found'})\n", "            continue\n", "\n", "        df = pd.read_csv(file_path, index_col=\"year\")\n", "        \n", "        # 【修改】将原始数据与年度ICA特征合并\n", "        df_merged = df.join(ica_features_df, how='left')\n", "        df_processed = df_merged.fillna(0)\n", "        \n", "        # 【修改】区分原始目标特征和所有输入特征\n", "        original_numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "        all_numeric_cols = df_processed.select_dtypes(include=[np.number]).columns.tolist()\n", "        \n", "        if df.empty or not original_numeric_cols:\n", "            print(f\"  !! 数据为空或无数值列: {file_path}。跳过。\")\n", "            all_age_group_metrics.append({'Age_Group': selected_age, 'Status': 'Empty Data or No Numeric Cols'})\n", "            continue\n", "            \n", "        N_TARGET_FEATURES = len(original_numeric_cols)\n", "        N_TOTAL_FEATURES = len(all_numeric_cols)\n", "        print(f\"  原始目标特征数量: {N_TARGET_FEATURES}, 总输入特征数量: {N_TOTAL_FEATURES}\")\n", "        \n", "        train_data_pd = df_processed.loc[TRAIN_START_YEAR:TRAIN_END_YEAR]\n", "        test_data_pd_for_eval = df_processed.loc[TEST_START_YEAR:TEST_END_YEAR]\n", "    \n", "        if len(train_data_pd) < SEQUENCE_LENGTH + 1:\n", "            print(f\"  !! 训练数据不足 (length: {len(train_data_pd)})。跳过。\")\n", "            all_age_group_metrics.append({'Age_Group': selected_age, 'Status': 'Insufficient Training Data'})\n", "            continue\n", "        \n", "        # --- 2.2 数据缩放 ---\n", "        scalers = {}\n", "        scaled_train_data = train_data_pd.copy()\n", "        for col in all_numeric_cols:\n", "            scaler = MinMaxScaler(feature_range=(-1, 1))\n", "            scaled_train_data[col] = scaler.fit_transform(train_data_pd[[col]].values)\n", "            scalers[col] = scaler\n", "        \n", "        scaled_full_data = df_processed.copy()\n", "        for col in all_numeric_cols:\n", "            if col in scalers:\n", "                scaled_full_data[col] = scalers[col].transform(df_processed[[col]].values)\n", "            else:\n", "                scaled_full_data[col] = 0\n", "\n", "        # --- 2.3 Dataset 和 DataLoader ---\n", "        train_numpy_scaled = scaled_train_data.values\n", "        try:\n", "            # 【修改】创建Dataset时传入目标特征数\n", "            train_dataset = TimeSeriesDeviationDataset(train_numpy_scaled, SEQUENCE_LENGTH, N_TARGET_FEATURES)\n", "            train_dataloader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, drop_last=True)\n", "            if len(train_dataloader) == 0: raise ValueError(\"DataLoader is empty after drop_last=True\")\n", "        except ValueError as e:\n", "            print(f\"  !! 创建Dataset/DataLoader时出错: {e}。跳过。\")\n", "            all_age_group_metrics.append({'Age_Group': selected_age, 'Status': 'DataLoader Creation Error'})\n", "            continue\n", "    \n", "        # --- 2.4 模型训练 ---\n", "        # 【修改】实例化模型时，传入正确的 input_size 和 output_size\n", "        model = Seq2SeqLNN_AttentionForecastModel(\n", "            input_size=N_TOTAL_FEATURES,\n", "            encoder_hidden_size=ENCODER_HIDDEN_SIZE,\n", "            output_size=N_TARGET_FEATURES,\n", "            num_encoder_layers=NUM_ENCODER_LAYERS,\n", "            solver_iterations=SOLVER_ITERATIONS,\n", "            activation_fn_choice=ACTIVATION_FN_CHOICE\n", "        ).to(device)\n", "        \n", "        criterion = nn.MS<PERSON><PERSON>()\n", "        optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)\n", "    \n", "        print(f\"\\n  开始为 {selected_age} 进行模型训练...\")\n", "        # (训练循环保持不变)\n", "        for epoch in range(EPOCHS):\n", "            # ... 训练逻辑 ...\n", "            model.train()\n", "            for x_dev, means, y_dev_targets in train_dataloader:\n", "                optimizer.zero_grad()\n", "                pred_dev = model(x_dev)\n", "                loss = criterion(pred_dev, y_dev_targets)\n", "                if torch.isnan(loss): break\n", "                loss.backward()\n", "                torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)\n", "                optimizer.step()\n", "            if 'loss' in locals() and isinstance(loss, torch.Tensor) and torch.isnan(loss):\n", "                print(f\"  !! 训练因NaN Loss中止。\")\n", "                break\n", "        \n", "        # --- 2.5 迭代预测 ---\n", "        print(f\"\\n  开始为 {selected_age} 进行迭代多步预测...\")\n", "        model.eval()\n", "        yearly_metrics_list = []\n", "        all_predictions_original_scale_df = pd.DataFrame(columns=original_numeric_cols, index=range(TEST_START_YEAR, TEST_END_YEAR + 1))\n", "        current_input_sequence_scaled_numpy = scaled_train_data.values[-SEQUENCE_LENGTH:]\n", "\n", "        with torch.no_grad():\n", "            for i in range(N_FORECAST_YEARS):\n", "                current_predict_year = TEST_START_YEAR + i\n", "                \n", "                # 【修改】使用新的预测逻辑\n", "                mean_of_current_input_scaled = np.mean(current_input_sequence_scaled_numpy, axis=0)\n", "                input_deviation_tensor = torch.FloatTensor(current_input_sequence_scaled_numpy - mean_of_current_input_scaled).unsqueeze(0).to(device)\n", "                predicted_deviation_scaled_numpy_flat = model(input_deviation_tensor).cpu().numpy().flatten()\n", "                mean_for_target = mean_of_current_input_scaled[:N_TARGET_FEATURES]\n", "                predicted_target_features_scaled = predicted_deviation_scaled_numpy_flat + mean_for_target\n", "\n", "                forecast_original_scale_year = []\n", "                for k, col_name in enumerate(original_numeric_cols):\n", "                    val_original = scalers[col_name].inverse_transform(predicted_target_features_scaled[k].reshape(-1, 1))\n", "                    forecast_original_scale_year.append(val_original[0, 0])\n", "                all_predictions_original_scale_df.loc[current_predict_year] = forecast_original_scale_year\n", "                \n", "                if current_predict_year in scaled_full_data.index:\n", "                    true_ica_features_scaled = scaled_full_data.loc[current_predict_year, all_numeric_cols[N_TARGET_FEATURES:]].values\n", "                else:\n", "                    true_ica_features_scaled = np.zeros(N_TOTAL_FEATURES - N_TARGET_FEATURES)\n", "                \n", "                next_full_year_scaled = np.concatenate([predicted_target_features_scaled, true_ica_features_scaled])\n", "                current_input_sequence_scaled_numpy = np.vstack([current_input_sequence_scaled_numpy[1:], next_full_year_scaled.reshape(1, -1)])\n", "                \n", "                # 评估\n", "                metrics_for_year = {'Year': current_predict_year, 'R2_Score': np.nan, 'MSE': np.nan, 'MAE': np.nan, 'MAPE': np.nan}\n", "                if not test_data_pd_for_eval.empty and current_predict_year in test_data_pd_for_eval.index:\n", "                    true_vals = test_data_pd_for_eval.loc[current_predict_year, original_numeric_cols].values.flatten()\n", "                    pred_vals = np.array(forecast_original_scale_year).flatten()\n", "                    metrics_for_year['R2_Score'] = r2_score(true_vals, pred_vals)\n", "                    metrics_for_year['MSE'] = mean_squared_error(true_vals, pred_vals)\n", "                    metrics_for_year['MAE'] = mean_absolute_error(true_vals, pred_vals)\n", "                    metrics_for_year['MAPE'] = mean_absolute_percentage_error(true_vals, pred_vals)\n", "                yearly_metrics_list.append(metrics_for_year)\n", "        \n", "        # --- 2.6 结果保存与总结 ---\n", "        predictions_csv_path = os.path.join(output_base_folder, f\"{selected_age}_predictions_{target}.csv\")\n", "        all_predictions_original_scale_df.to_csv(predictions_csv_path)\n", "        \n", "        metrics_df_age = pd.DataFrame(yearly_metrics_list).set_index('Year')\n", "        metrics_summary = {'Age_Group': selected_age, 'Status': 'Success'}\n", "        for metric in ['R2_Score', 'MSE', 'MAE', 'MAPE']:\n", "            metrics_summary[f'Avg_{metric}'] = metrics_df_age[metric].mean(skipna=True)\n", "        all_age_group_metrics.append(metrics_summary)\n", "        \n", "        metrics_csv_path = os.path.join(output_base_folder, f\"{selected_age}_metrics.csv\")\n", "        metrics_df_age.to_csv(metrics_csv_path)\n", "\n", "        # 绘图 (只绘制第一个目标特征)\n", "        if original_numeric_cols:\n", "            feature_name_to_plot = original_numeric_cols[0]\n", "            plt.figure(figsize=(15, 7))\n", "            plt.plot(train_data_pd.index, train_data_pd[feature_name_to_plot], label=f\"训练数据\", marker='.', color='gray')\n", "            if not test_data_pd_for_eval.empty:\n", "                plt.plot(test_data_pd_for_eval.index, test_data_pd_for_eval[feature_name_to_plot], label=f\"真实值\", marker='o', color='green')\n", "            plt.plot(all_predictions_original_scale_df.index, all_predictions_original_scale_df[feature_name_to_plot], label=f\"预测值 (含ICA)\", marker='x', linestyle='--', color='dodgerblue')\n", "            plt.title(f\"{feature_name_to_plot} - {target_disease_name} - {selected_age} - 预测对比\")\n", "            plt.legend()\n", "            plt.grid(True, linestyle=':', alpha=0.7)\n", "            forecast_plot_path = os.path.join(output_base_folder, f\"{selected_age}_{feature_name_to_plot}_forecast.png\")\n", "            plt.savefig(forecast_plot_path)\n", "            plt.close()\n", "            \n", "        print(f\"--- 年龄段 {selected_age} 处理完毕 ---\")\n", "        plt.close('all') # 确保关闭所有图形，防止内存泄漏\n", "    \n", "    # --- 循环结束后的总结 ---\n", "    summary_df = pd.DataFrame(all_age_group_metrics)\n", "    summary_csv_path = os.path.join(output_base_folder, f\"_summary_all_age_groups_metrics.csv\")\n", "    summary_df.to_csv(summary_csv_path, index=False)\n", "    print(f\"\\n\\n{'='*25} 疾病 {target_disease_name} 处理完成 {'='*25}\")\n", "    print(f\"所有年龄段的平均指标概要已保存: {summary_csv_path}\")\n", "    print(summary_df)\n", "\n", "print(\"\\n\\n所有任务完成。\")"], "metadata": {"collapsed": false, "is_executing": true, "ExecuteTime": {"start_time": "2025-06-19T08:30:23.058686900Z"}}, "id": "a2ec041abfeea81"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "e4f3918fbdb0b562"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}