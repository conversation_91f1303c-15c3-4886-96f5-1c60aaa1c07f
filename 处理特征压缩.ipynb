{"cells": [{"cell_type": "code", "execution_count": 2, "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-19T07:29:17.884557400Z", "start_time": "2025-06-19T07:29:17.404805300Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "features_part1=pd.read_csv(\"协变量部分/筛选后协变量特征值列表.csv\",index_col=[0])\n", "features_part2=pd.read_csv(\"协变量部分/CMIP6_ssp126_1950-2100.csv\",index_col=[0])"]}, {"cell_type": "code", "execution_count": 5, "outputs": [], "source": ["features_part2=features_part2.reset_index()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:30:33.714408200Z", "start_time": "2025-06-19T07:30:33.689897900Z"}}, "id": "cf3a7c2811157b3f"}, {"cell_type": "code", "execution_count": 6, "outputs": [{"data": {"text/plain": "      location    year  SM.POP.NETM  SP.POP.0004.FE.5Y  SP.POP.0004.MA.5Y  \\\n0  Afghanistan  1960.0       2606.0          17.449375          16.452922   \n1  Afghanistan  1961.0       6109.0          17.553984          16.618759   \n2  Afghanistan  1962.0       7016.0          17.656564          16.780175   \n3  Afghanistan  1963.0       6681.0          17.758778          16.938428   \n4  Afghanistan  1964.0       7079.0          17.860351          17.094602   \n\n   SP.POP.0014.FE.ZS  SP.POP.0014.MA.ZS  SP.POP.0014.TO.ZS  SP.POP.0509.FE.5Y  \\\n0          43.055443          40.304402          41.627186          13.259004   \n1          43.034330          40.451081          41.695303          13.356603   \n2          42.995971          40.625684          41.769167          13.451976   \n3          42.974641          40.867099          41.885377          13.543510   \n4          43.019156          41.159672          42.059389          13.633544   \n\n   SP.POP.0509.MA.5Y  ...  FM.AST.PRVT.ZG.M3  MS.MIL.XPND.CD  MS.MIL.XPND.CN  \\\n0          12.501014  ...          -0.802823    6.568119e+06    1.715638e+09   \n1          12.645607  ...          -2.519201    3.786540e+06    1.472029e+09   \n2          12.788331  ...         -11.197772    3.154864e+06    1.242649e+09   \n3          12.926272  ...          -1.111111    3.168219e+06    1.259201e+09   \n4          13.060058  ...           3.149928    3.729214e+06    1.627004e+09   \n\n   DT.ODA.ODAT.CD  DT.ODA.ODAT.KD  DT.ODA.ODAT.PC.ZS  DC.DAC.DEUL.CD  \\\n0    1.718000e+07    1.259500e+08           1.992469    6.500000e+05   \n1    3.467000e+07    2.501800e+08           3.944192    3.380000e+06   \n2    1.693000e+07    1.217100e+08           1.887603    2.150000e+06   \n3    3.667000e+07    2.563200e+08           4.004383    2.810000e+06   \n4    4.617000e+07    3.197300e+08           4.935057    6.630000e+06   \n\n   MS.MIL.XPND.GD.ZS  EN.URB.MCTY  EN.URB.MCTY.TL.ZS  \n0           2.215035     285352.0           3.309401  \n1           1.860295     300359.0           3.416999  \n2           1.527293     316177.0           3.525202  \n3           1.540817     332829.0           3.634510  \n4           1.749029     350382.0           3.745192  \n\n[5 rows x 299 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>location</th>\n      <th>year</th>\n      <th>SM.POP.NETM</th>\n      <th>SP.POP.0004.FE.5Y</th>\n      <th>SP.POP.0004.MA.5Y</th>\n      <th>SP.POP.0014.FE.ZS</th>\n      <th>SP.POP.0014.MA.ZS</th>\n      <th>SP.POP.0014.TO.ZS</th>\n      <th>SP.POP.0509.FE.5Y</th>\n      <th>SP.POP.0509.MA.5Y</th>\n      <th>...</th>\n      <th>FM.AST.PRVT.ZG.M3</th>\n      <th>MS.MIL.XPND.CD</th>\n      <th>MS.MIL.XPND.CN</th>\n      <th>DT.ODA.ODAT.CD</th>\n      <th>DT.ODA.ODAT.KD</th>\n      <th>DT.ODA.ODAT.PC.ZS</th>\n      <th>DC.DAC.DEUL.CD</th>\n      <th>MS.MIL.XPND.GD.ZS</th>\n      <th>EN.URB.MCTY</th>\n      <th>EN.URB.MCTY.TL.ZS</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>Afghanistan</td>\n      <td>1960.0</td>\n      <td>2606.0</td>\n      <td>17.449375</td>\n      <td>16.452922</td>\n      <td>43.055443</td>\n      <td>40.304402</td>\n      <td>41.627186</td>\n      <td>13.259004</td>\n      <td>12.501014</td>\n      <td>...</td>\n      <td>-0.802823</td>\n      <td>6.568119e+06</td>\n      <td>1.715638e+09</td>\n      <td>1.718000e+07</td>\n      <td>1.259500e+08</td>\n      <td>1.992469</td>\n      <td>6.500000e+05</td>\n      <td>2.215035</td>\n      <td>285352.0</td>\n      <td>3.309401</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>Afghanistan</td>\n      <td>1961.0</td>\n      <td>6109.0</td>\n      <td>17.553984</td>\n      <td>16.618759</td>\n      <td>43.034330</td>\n      <td>40.451081</td>\n      <td>41.695303</td>\n      <td>13.356603</td>\n      <td>12.645607</td>\n      <td>...</td>\n      <td>-2.519201</td>\n      <td>3.786540e+06</td>\n      <td>1.472029e+09</td>\n      <td>3.467000e+07</td>\n      <td>2.501800e+08</td>\n      <td>3.944192</td>\n      <td>3.380000e+06</td>\n      <td>1.860295</td>\n      <td>300359.0</td>\n      <td>3.416999</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>Afghanistan</td>\n      <td>1962.0</td>\n      <td>7016.0</td>\n      <td>17.656564</td>\n      <td>16.780175</td>\n      <td>42.995971</td>\n      <td>40.625684</td>\n      <td>41.769167</td>\n      <td>13.451976</td>\n      <td>12.788331</td>\n      <td>...</td>\n      <td>-11.197772</td>\n      <td>3.154864e+06</td>\n      <td>1.242649e+09</td>\n      <td>1.693000e+07</td>\n      <td>1.217100e+08</td>\n      <td>1.887603</td>\n      <td>2.150000e+06</td>\n      <td>1.527293</td>\n      <td>316177.0</td>\n      <td>3.525202</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>Afghanistan</td>\n      <td>1963.0</td>\n      <td>6681.0</td>\n      <td>17.758778</td>\n      <td>16.938428</td>\n      <td>42.974641</td>\n      <td>40.867099</td>\n      <td>41.885377</td>\n      <td>13.543510</td>\n      <td>12.926272</td>\n      <td>...</td>\n      <td>-1.111111</td>\n      <td>3.168219e+06</td>\n      <td>1.259201e+09</td>\n      <td>3.667000e+07</td>\n      <td>2.563200e+08</td>\n      <td>4.004383</td>\n      <td>2.810000e+06</td>\n      <td>1.540817</td>\n      <td>332829.0</td>\n      <td>3.634510</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>Afghanistan</td>\n      <td>1964.0</td>\n      <td>7079.0</td>\n      <td>17.860351</td>\n      <td>17.094602</td>\n      <td>43.019156</td>\n      <td>41.159672</td>\n      <td>42.059389</td>\n      <td>13.633544</td>\n      <td>13.060058</td>\n      <td>...</td>\n      <td>3.149928</td>\n      <td>3.729214e+06</td>\n      <td>1.627004e+09</td>\n      <td>4.617000e+07</td>\n      <td>3.197300e+08</td>\n      <td>4.935057</td>\n      <td>6.630000e+06</td>\n      <td>1.749029</td>\n      <td>350382.0</td>\n      <td>3.745192</td>\n    </tr>\n  </tbody>\n</table>\n<p>5 rows × 299 columns</p>\n</div>"}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["features_part1.head()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:30:35.498700200Z", "start_time": "2025-06-19T07:30:35.460623300Z"}}, "id": "20f2282ae07da364"}, {"cell_type": "code", "execution_count": 7, "outputs": [{"data": {"text/plain": "  location  year       bldep           ch4            co         drybc  \\\n0  Albania  1950  720.297059  9.936524e-07  8.289784e-08  1.474547e-13   \n1  Albania  1951  715.111196  9.984426e-07  8.212097e-08  1.575895e-13   \n2  Albania  1952  699.299460  1.004202e-06  8.143293e-08  1.531231e-13   \n3  Albania  1953  720.849415  1.010818e-06  7.971061e-08  1.510502e-13   \n4  Albania  1954  695.398128  1.021187e-06  7.673328e-08  1.682507e-13   \n\n        drydust        drynh3        drynh4         dryo3  ...      tasmin  \\\n0  4.669871e-11  2.842485e-12  7.636505e-13  6.591502e-11  ...  284.017587   \n1  6.589192e-11  2.935262e-12  7.873252e-13  6.471604e-11  ...  284.180779   \n2  4.994490e-11  3.004318e-12  8.347433e-13  6.538824e-11  ...  284.540792   \n3  5.401665e-11  3.121748e-12  8.953634e-13  6.662666e-11  ...  284.291300   \n4  4.446454e-11  3.170958e-12  9.034765e-13  6.830762e-11  ...  284.123117   \n\n           ts         wetbc       wetdust        wetnh3        wetnh4  \\\n0  289.121841  5.367130e-13  2.994392e-10  1.405334e-12  3.251532e-12   \n1  289.357947  5.979240e-13  3.666691e-10  1.026755e-12  3.290657e-12   \n2  289.327431  5.573262e-13  2.588767e-10  1.458054e-12  3.891814e-12   \n3  289.294224  5.834731e-13  3.589112e-10  1.320073e-12  3.919259e-12   \n4  289.083824  6.339439e-13  2.722372e-10  1.104508e-12  3.770566e-12   \n\n         wetnoy         wetoa        wetso2        wetso4  \n0  2.119282e-12  3.606237e-12  7.327736e-12  2.247552e-11  \n1  2.206014e-12  3.559631e-12  9.257575e-12  2.576007e-11  \n2  2.388510e-12  3.776014e-12  9.939880e-12  2.310391e-11  \n3  2.435859e-12  3.574918e-12  8.429546e-12  2.536960e-11  \n4  2.460145e-12  3.631899e-12  8.320349e-12  2.617592e-11  \n\n[5 rows x 73 columns]", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>location</th>\n      <th>year</th>\n      <th>bldep</th>\n      <th>ch4</th>\n      <th>co</th>\n      <th>drybc</th>\n      <th>drydust</th>\n      <th>drynh3</th>\n      <th>drynh4</th>\n      <th>dryo3</th>\n      <th>...</th>\n      <th>tasmin</th>\n      <th>ts</th>\n      <th>wetbc</th>\n      <th>wetdust</th>\n      <th>wetnh3</th>\n      <th>wetnh4</th>\n      <th>wetnoy</th>\n      <th>wetoa</th>\n      <th>wetso2</th>\n      <th>wetso4</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>Albania</td>\n      <td>1950</td>\n      <td>720.297059</td>\n      <td>9.936524e-07</td>\n      <td>8.289784e-08</td>\n      <td>1.474547e-13</td>\n      <td>4.669871e-11</td>\n      <td>2.842485e-12</td>\n      <td>7.636505e-13</td>\n      <td>6.591502e-11</td>\n      <td>...</td>\n      <td>284.017587</td>\n      <td>289.121841</td>\n      <td>5.367130e-13</td>\n      <td>2.994392e-10</td>\n      <td>1.405334e-12</td>\n      <td>3.251532e-12</td>\n      <td>2.119282e-12</td>\n      <td>3.606237e-12</td>\n      <td>7.327736e-12</td>\n      <td>2.247552e-11</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>Albania</td>\n      <td>1951</td>\n      <td>715.111196</td>\n      <td>9.984426e-07</td>\n      <td>8.212097e-08</td>\n      <td>1.575895e-13</td>\n      <td>6.589192e-11</td>\n      <td>2.935262e-12</td>\n      <td>7.873252e-13</td>\n      <td>6.471604e-11</td>\n      <td>...</td>\n      <td>284.180779</td>\n      <td>289.357947</td>\n      <td>5.979240e-13</td>\n      <td>3.666691e-10</td>\n      <td>1.026755e-12</td>\n      <td>3.290657e-12</td>\n      <td>2.206014e-12</td>\n      <td>3.559631e-12</td>\n      <td>9.257575e-12</td>\n      <td>2.576007e-11</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>Albania</td>\n      <td>1952</td>\n      <td>699.299460</td>\n      <td>1.004202e-06</td>\n      <td>8.143293e-08</td>\n      <td>1.531231e-13</td>\n      <td>4.994490e-11</td>\n      <td>3.004318e-12</td>\n      <td>8.347433e-13</td>\n      <td>6.538824e-11</td>\n      <td>...</td>\n      <td>284.540792</td>\n      <td>289.327431</td>\n      <td>5.573262e-13</td>\n      <td>2.588767e-10</td>\n      <td>1.458054e-12</td>\n      <td>3.891814e-12</td>\n      <td>2.388510e-12</td>\n      <td>3.776014e-12</td>\n      <td>9.939880e-12</td>\n      <td>2.310391e-11</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>Albania</td>\n      <td>1953</td>\n      <td>720.849415</td>\n      <td>1.010818e-06</td>\n      <td>7.971061e-08</td>\n      <td>1.510502e-13</td>\n      <td>5.401665e-11</td>\n      <td>3.121748e-12</td>\n      <td>8.953634e-13</td>\n      <td>6.662666e-11</td>\n      <td>...</td>\n      <td>284.291300</td>\n      <td>289.294224</td>\n      <td>5.834731e-13</td>\n      <td>3.589112e-10</td>\n      <td>1.320073e-12</td>\n      <td>3.919259e-12</td>\n      <td>2.435859e-12</td>\n      <td>3.574918e-12</td>\n      <td>8.429546e-12</td>\n      <td>2.536960e-11</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>Albania</td>\n      <td>1954</td>\n      <td>695.398128</td>\n      <td>1.021187e-06</td>\n      <td>7.673328e-08</td>\n      <td>1.682507e-13</td>\n      <td>4.446454e-11</td>\n      <td>3.170958e-12</td>\n      <td>9.034765e-13</td>\n      <td>6.830762e-11</td>\n      <td>...</td>\n      <td>284.123117</td>\n      <td>289.083824</td>\n      <td>6.339439e-13</td>\n      <td>2.722372e-10</td>\n      <td>1.104508e-12</td>\n      <td>3.770566e-12</td>\n      <td>2.460145e-12</td>\n      <td>3.631899e-12</td>\n      <td>8.320349e-12</td>\n      <td>2.617592e-11</td>\n    </tr>\n  </tbody>\n</table>\n<p>5 rows × 73 columns</p>\n</div>"}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["features_part2.head()"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:30:36.042648100Z", "start_time": "2025-06-19T07:30:35.926106700Z"}}, "id": "dc99f6d8397977a5"}, {"cell_type": "code", "execution_count": 9, "outputs": [], "source": ["features_part2[\"year\"]=features_part2[\"year\"].astype(\"int\")\n", "features_part1[\"year\"]=features_part1[\"year\"].astype(\"int\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:34:36.190944600Z", "start_time": "2025-06-19T07:34:36.176417900Z"}}, "id": "fef0a65d478f634e"}, {"cell_type": "code", "execution_count": 11, "outputs": [], "source": ["feature_all=pd.merge(features_part1,features_part2,on=[\"location\",\"year\"])"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:34:58.093272400Z", "start_time": "2025-06-19T07:34:58.026175900Z"}}, "id": "c2013ccb142ff16b"}, {"cell_type": "code", "execution_count": 13, "outputs": [{"data": {"text/plain": "(12416, 299)"}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["features_part1.shape"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:35:17.592478900Z", "start_time": "2025-06-19T07:35:17.547808500Z"}}, "id": "d9bb0fa917e2dabb"}, {"cell_type": "code", "execution_count": 12, "outputs": [{"data": {"text/plain": "(12416, 370)"}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["feature_all.shape"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:35:04.064744600Z", "start_time": "2025-06-19T07:35:04.037208300Z"}}, "id": "93602c83ade9771d"}, {"cell_type": "code", "execution_count": 15, "outputs": [{"data": {"text/plain": "      location  year  SM.POP.NETM  SP.POP.0004.FE.5Y  SP.POP.0004.MA.5Y  \\\n0  Afghanistan  1960       2606.0          17.449375          16.452922   \n1  Afghanistan  1961       6109.0          17.553984          16.618759   \n2  Afghanistan  1962       7016.0          17.656564          16.780175   \n3  Afghanistan  1963       6681.0          17.758778          16.938428   \n4  Afghanistan  1964       7079.0          17.860351          17.094602   \n\n   SP.POP.0014.FE.ZS  SP.POP.0014.MA.ZS  SP.POP.0014.TO.ZS  SP.POP.0509.FE.5Y  \\\n0          43.055443          40.304402          41.627186          13.259004   \n1          43.034330          40.451081          41.695303          13.356603   \n2          42.995971          40.625684          41.769167          13.451976   \n3          42.974641          40.867099          41.885377          13.543510   \n4          43.019156          41.159672          42.059389          13.633544   \n\n   SP.POP.0509.MA.5Y  \n0          12.501014  \n1          12.645607  \n2          12.788331  \n3          12.926272  \n4          13.060058  ", "text/html": "<div>\n<style scoped>\n    .dataframe tbody tr th:only-of-type {\n        vertical-align: middle;\n    }\n\n    .dataframe tbody tr th {\n        vertical-align: top;\n    }\n\n    .dataframe thead th {\n        text-align: right;\n    }\n</style>\n<table border=\"1\" class=\"dataframe\">\n  <thead>\n    <tr style=\"text-align: right;\">\n      <th></th>\n      <th>location</th>\n      <th>year</th>\n      <th>SM.POP.NETM</th>\n      <th>SP.POP.0004.FE.5Y</th>\n      <th>SP.POP.0004.MA.5Y</th>\n      <th>SP.POP.0014.FE.ZS</th>\n      <th>SP.POP.0014.MA.ZS</th>\n      <th>SP.POP.0014.TO.ZS</th>\n      <th>SP.POP.0509.FE.5Y</th>\n      <th>SP.POP.0509.MA.5Y</th>\n    </tr>\n  </thead>\n  <tbody>\n    <tr>\n      <th>0</th>\n      <td>Afghanistan</td>\n      <td>1960</td>\n      <td>2606.0</td>\n      <td>17.449375</td>\n      <td>16.452922</td>\n      <td>43.055443</td>\n      <td>40.304402</td>\n      <td>41.627186</td>\n      <td>13.259004</td>\n      <td>12.501014</td>\n    </tr>\n    <tr>\n      <th>1</th>\n      <td>Afghanistan</td>\n      <td>1961</td>\n      <td>6109.0</td>\n      <td>17.553984</td>\n      <td>16.618759</td>\n      <td>43.034330</td>\n      <td>40.451081</td>\n      <td>41.695303</td>\n      <td>13.356603</td>\n      <td>12.645607</td>\n    </tr>\n    <tr>\n      <th>2</th>\n      <td>Afghanistan</td>\n      <td>1962</td>\n      <td>7016.0</td>\n      <td>17.656564</td>\n      <td>16.780175</td>\n      <td>42.995971</td>\n      <td>40.625684</td>\n      <td>41.769167</td>\n      <td>13.451976</td>\n      <td>12.788331</td>\n    </tr>\n    <tr>\n      <th>3</th>\n      <td>Afghanistan</td>\n      <td>1963</td>\n      <td>6681.0</td>\n      <td>17.758778</td>\n      <td>16.938428</td>\n      <td>42.974641</td>\n      <td>40.867099</td>\n      <td>41.885377</td>\n      <td>13.543510</td>\n      <td>12.926272</td>\n    </tr>\n    <tr>\n      <th>4</th>\n      <td>Afghanistan</td>\n      <td>1964</td>\n      <td>7079.0</td>\n      <td>17.860351</td>\n      <td>17.094602</td>\n      <td>43.019156</td>\n      <td>41.159672</td>\n      <td>42.059389</td>\n      <td>13.633544</td>\n      <td>13.060058</td>\n    </tr>\n  </tbody>\n</table>\n</div>"}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["feature_all.head().iloc[:5,:10]"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:35:45.633233500Z", "start_time": "2025-06-19T07:35:45.605722700Z"}}, "id": "dae73b832d24ae79"}, {"cell_type": "code", "execution_count": 17, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["按年份处理数据: 100%|██████████| 64/64 [00:08<00:00,  7.17it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "处理完成！\n", "\n", "\n", "最终结果的DataFrame形式:\n", "       component_0   component_1   component_2   component_3   component_4  \\\n", "1960  3.891503e-17  2.518032e-17  1.373472e-17  1.602384e-17  5.722799e-17   \n", "1961 -5.264975e-17 -2.289120e-18  3.433679e-17 -2.975856e-17  2.289120e-17   \n", "1962  9.156479e-18  5.493887e-17 -6.867359e-18 -2.746944e-17 -2.289120e-17   \n", "1963  9.156479e-18 -3.662591e-17  4.807151e-17  6.867359e-18  1.373472e-17   \n", "1964  3.433679e-17  5.036063e-17  2.289120e-18  1.373472e-17  2.289120e-18   \n", "\n", "       component_5   component_6   component_7   component_8   component_9  \\\n", "1960 -1.831296e-17  1.373472e-17 -3.891503e-17  6.867359e-18 -1.831296e-17   \n", "1961  2.289120e-18  2.289120e-18  1.602384e-17 -3.891503e-17 -2.289120e-17   \n", "1962  2.289120e-18  9.156479e-18 -1.602384e-17 -4.578239e-17  7.554095e-17   \n", "1963  2.289120e-17 -6.867359e-17  1.373472e-17 -5.036063e-17  4.578239e-18   \n", "1964 -4.578239e-18 -4.578239e-18 -2.060208e-17 -3.891503e-17  5.722799e-17   \n", "\n", "      ...  component_54  component_55  component_56  component_57  \\\n", "1960  ... -1.373472e-17  4.120415e-17 -4.578239e-18 -3.548135e-17   \n", "1961  ... -9.156479e-18  3.433679e-17 -1.602384e-17  3.891503e-17   \n", "1962  ...  1.144560e-17  2.289120e-18  6.867359e-18 -1.144560e-17   \n", "1963  ...  2.289120e-17  1.831296e-17  4.349327e-17  4.578239e-17   \n", "1964  ... -1.831296e-17 -2.289120e-17 -1.144560e-17  1.831296e-17   \n", "\n", "      component_58  component_59  component_60  component_61  component_62  \\\n", "1960  9.156479e-18 -2.975856e-17  4.578239e-18  6.867359e-18 -1.373472e-17   \n", "1961  1.602384e-17 -4.807151e-17 -4.120415e-17  4.578239e-17  1.602384e-17   \n", "1962  1.602384e-17 -2.289120e-17  4.578239e-18  2.289120e-18  2.289120e-17   \n", "1963  1.373472e-17 -2.289120e-18 -9.156479e-18  1.831296e-17 -2.746944e-17   \n", "1964 -4.578239e-18  4.578239e-18 -5.264975e-17  1.144560e-17 -1.144560e-17   \n", "\n", "      component_63  \n", "1960  2.518032e-17  \n", "1961 -1.373472e-17  \n", "1962  4.578239e-18  \n", "1963 -2.975856e-17  \n", "1964  4.692695e-17  \n", "\n", "[5 rows x 64 columns]\n", "\n", "最终DataFrame的形状: (64, 64)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# --- 步骤 2: 定义参数和准备工作 ---\n", "N_COMPONENTS = 64\n", "year_vectors = {}\n", "feature_columns = [col for col in feature_all.columns if col not in ['location', 'year']]\n", "\n", "# --- 步骤 3: 按年份分组并应用ICA ---\n", "grouped = feature_all.groupby('year')\n", "for year, group_df in tqdm(grouped, desc=\"按年份处理数据\"):\n", "\n", "    if len(group_df) < N_COMPONENTS:\n", "        print(f\"\\n警告: 年份 {year} 的样本数 ({len(group_df)}) 小于目标组件数 ({N_COMPONENTS})，已跳过。\")\n", "        continue\n", "\n", "    # --- 步骤 4: 数据预处理 ---\n", "    X_year = group_df[feature_columns]\n", "\n", "    # ####################################################################\n", "    # ##               ↓↓↓  这就是修复问题的关键代码  ↓↓↓               ##\n", "    # ####################################################################\n", "    # 在插补前，将所有无穷大值 (inf, -inf) 替换为 NaN\n", "    # 这样 SimpleImputer 就可以统一处理它们了\n", "    X_year = X_year.replace([np.inf, -np.inf], np.nan)\n", "    # ####################################################################\n", "\n", "    # 处理缺失值（NaN）。使用均值填充是一个简单有效的方法。\n", "    imputer = SimpleImputer(strategy='mean')\n", "    \n", "    # 这一步现在不会报错了，因为无穷大值已经被替换\n", "    X_imputed = imputer.fit_transform(X_year)\n", "\n", "    # --- 步骤 5: 应用 ICA ---\n", "    ica = FastICA(n_components=N_COMPONENTS, random_state=42, max_iter=1000, tol=0.01)\n", "    transformed_data = ica.fit_transform(X_imputed)\n", "\n", "    # --- 步骤 6: 聚合结果 ---\n", "    year_vector = transformed_data.mean(axis=0)\n", "\n", "    # --- 步骤 7: 存储结果 ---\n", "    year_vectors[year] = year_vector\n", "\n", "print(\"\\n处理完成！\")\n", "\n", "# --- 结果展示 ---\n", "result_df = pd.DataFrame.from_dict(year_vectors, orient='index',\n", "                                   columns=[f'component_{i}' for i in range(N_COMPONENTS)])\n", "print(\"\\n\\n最终结果的DataFrame形式:\")\n", "print(result_df.head())\n", "print(f\"\\n最终DataFrame的形状: {result_df.shape}\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:57:44.884634900Z", "start_time": "2025-06-19T07:57:35.936806700Z"}}, "id": "a8efd03ffa9dda86"}, {"cell_type": "code", "execution_count": 19, "outputs": [], "source": ["result_df.to_csv(\"ICA处理合并特征值.csv\")"], "metadata": {"collapsed": false, "ExecuteTime": {"end_time": "2025-06-19T07:59:19.657076300Z", "start_time": "2025-06-19T07:59:19.612050300Z"}}, "id": "d99c48b436b8e2a4"}, {"cell_type": "code", "execution_count": null, "outputs": [], "source": [], "metadata": {"collapsed": false}, "id": "f031ac45c1e5642c"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}